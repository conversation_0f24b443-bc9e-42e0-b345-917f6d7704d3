<template>
  <div class="teach-summary">
    <div class="content">
      <div class="content-left">
        <span class="icon-box">
          <img src="@/assets/images/teach_summary_1.png" alt="" />
        </span>
        <span>教学亮点</span>
      </div>
      <p class="content-right ellipsis">
        <template v-for="(item, index) in adviceData18['亮点']" :key="index">{{ index + 1 }}.{{ item }}<br /></template>
      </p>
    </div>
    <div class="content">
      <div class="content-left">
        <span class="icon-box">
          <img src="@/assets/images/teach_summary_2.png" alt="" />
        </span>
        <span>学生体验和启发</span>
      </div>
      <p class="content-right ellipsis">
        <template v-for="(item, index) in experienceAndInspiration" :key="index">{{ index + 1 }}.{{ item }}<br /></template>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref, onMounted, computed } from 'vue';

const roleChats = inject<Ref<any>>("roleChats");

onMounted(() => {
  initData();
});

const adviceData18 = ref<any>([]);

const initData = () => {
  if (roleChats?.value) {
    const roleChat3 = roleChats?.value[3] as any[];
    if (!roleChat3) return;
    const data18 = roleChat3.find((item) => item.questionType === 18);
    if (data18 && data18.answer) {
      const data18Answer = JSON.parse(data18.answer);
      adviceData18.value = data18Answer;
    }
  }
}

const experienceAndInspiration = computed(() => {
  if (adviceData18.value && (adviceData18.value["体验"] || adviceData18.value["启发"])) {
    return [...adviceData18.value["体验"], ...adviceData18.value["启发"]];
  }
  return [];
});
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  height: 232px;
  &:first-child {
    .icon-box {
      background: #FAAD14;
    }
  }
  &:nth-child(2) {
    .icon-box {
      background: #13C2C2;
    }
  }
  &:not(:last-child) {
    border-bottom: 1px solid #e5e5e5;
  }
  .content-left {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 160px;
    border-right: 1px solid #e5e5e5;
    .icon-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 8px;
      img {
        width: 20px;
        height: 20px;
      }
    }
  }
  .content-right {
    flex: 1;
    padding: 18px 20px;
    p {
      margin-bottom: 0;
      line-height: 22px;
    }
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 9;
}
</style>
